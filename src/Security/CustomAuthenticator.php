<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;
use Sparefoot\MyFootService\Service\User;
use Sparefoot\MyFootService\Service\UserOauth;
use Sparefoot\MyFootService\Service\UserRememberMe;
use Sparefoot\MyFootService\Service\UserRedirect;

class CustomAuthenticator extends AbstractAuthenticator
{
    private UrlGeneratorInterface $urlGenerator;
    private $csrfTokenManager;
    private $request;

    public function __construct(
        CsrfTokenManagerInterface $csrfTokenManager,
        UrlGeneratorInterface $urlGenerator,
    ) {
        $this->csrfTokenManager = $csrfTokenManager;
        $this->urlGenerator = $urlGenerator;
    }

    public function supports(Request $request): ?bool
    {
        return $request->attributes->get('_route') === 'login_check' && $request->isMethod('POST');
    }

    public function authenticate(Request $request): Passport
    {
        // if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'local') {
        //     // In local environment, use the fake local authentication

        //     return $this->authenticateFakeLocal($request);
        // }
        $this->request = $request;
        $csrfToken = $request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('authenticate', $csrfToken))) {
            throw new AuthenticationException('Invalid CSRF token.');
        }

        $email = $request->get('_username');
        $password = $request->get('_password');
        if (empty($email)) {
            throw new AuthenticationException('Email cannot be empty');
        }
        $adapter = new \Genesis_Util_AuthAdapter($email, $password);

        $genesisUser = $adapter->authenticate(\Genesis_Entity_UserAccess::APP_MYFOOT);

        if (!$genesisUser) {
            throw new AuthenticationException('Invalid credentials');
        }
        dump($request->get('_remember_me'));
        exit;
        $this->processLoginAction($request);

        $request->getSession()->set(Security::LAST_USERNAME, $genesisUser->getEmail());

        $badges = [];
        if ($request->request->get('_remember_me')) {
            $badges[] = new RememberMeBadge();
        }

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            $badges
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $user = $token->getUser();
        if (!$user instanceof User) {
            throw new \LogicException('The user must be an instance of Sparefoot\MyFootService\Security\User.');
        }

        // Default redirect if no specific role redirect is found
        return new RedirectResponse($this->urlGenerator->generate('dashboard_index'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        $request->getSession()->set(Security::AUTHENTICATION_ERROR, $exception);

        return new RedirectResponse($this->urlGenerator->generate('login_index'));
    }

    /**
     * This method is used for local environment authentication.
     * It simulates the authentication process without actually checking credentials.
     */
    public function authenticateFakeLocal(Request $request): void
    {
        /*
        $csrfToken = $request->request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('authenticate', $csrfToken))) {
            throw new AuthenticationException('Invalid CSRF token.');
        }

        $email = $request->request->get('_username');
        $password = $request->request->get('_password');
        if (empty($email)) {
            throw new AuthenticationException('Email cannot be empty');
        }

        $request->getSession()->set(Security::LAST_USERNAME, $email);

        $badges = [];
        if ($request->request->get('_remember_me')) {
            $badges[] = new RememberMeBadge();
        }

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            $badges
        );
        */
    }

    private function getParam($name, $default = null)
    {
        return $this->request->get($name, $default);
    }

    /**
     * Process user login
     * called from login form
     */
    private function processLoginAction()
    {
        $error = false;
        UserRememberMe::getSetRememberMe($this->getParam('_remember_me'), $this->getParam('_username'));

        $userAccess = false;
        try {
            \Genesis_Db_Connection::getInstance();
            UserOauth::authenticate($this->getParam('_username'), $this->getParam('_password'));
            $userAccess = UserOauth::getUserAccess();

        } catch (\Exception $e) {

            $userAccess = false;
            if (stripos($e->getMessage(), 'Invalid login')) {
                $error = 'Incorrect e-mail address or password. Please re-enter your credentials.';
            } else {
                $error = $e->getMessage();
            }
        }

        if (! $userAccess && ! $error) { //see why we need an error message still
            $ua = \Genesis_Service_UserAccess::loadByEmail($this->getParam('_username'));
            if ($ua && $ua->getAccount() && ! $ua->getMyfootRole()) {
                $error = 'LEGACY Directions to reset your password have been emailed to you. If you do not receive an email, please contact our Support team at 855-427-8193 for further assistance.';
            } elseif ($this->getParam('_username') || $this->getParam('_password')) {
                $error = 'LEGACY Incorrect e-mail address or password. Please re-enter your credentials.';
            } else {
                $error = 'LEGACY Please enter your e-mail address and password.';
            }
        }

        //login failed, set the error and redirect
        if (! $userAccess) {
            User::getSession()->loginError = $error;
            $this->redirect($this->view->url(['action'=>'index'], 'login'));
        }

        \Genesis_Service_UserAccess::updateLastLoggedIn($userAccess);

        if ($userAccess->isMyFootGod()) {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), null, true);
        } else {
            \Genesis_Util_FilteredIps::insertIp(\Genesis_Util_FilteredIps::getRemoteIp(), 'client');
        }

        //resume the page we were attempting
        if (UserRedirect::needsRedirect()) {

            $this->redirect(UserRedirect::getRedirect());
        }
        //normal myfoot entry
        $this->redirect($this->view->url(['action' => 'index'], 'dashboard'));
    }
}
